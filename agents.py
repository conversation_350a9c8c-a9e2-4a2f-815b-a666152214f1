import os
from dotenv import load_dotenv
from langchain_groq import ChatGroq
from autogen import AssistantAgent

load_dotenv()
groq_api_key = os.getenv("GROQ_API_KEY")


llm = ChatGroq(api_key=groq_api_key, model_name="llama3-70b-8192")

class GroqLLMWrapper:
    def __init__(self, llm):
        self.llm = llm

    def chat(self, messages, **kwargs):
        prompt = "\n".join([f"{m['role'].upper()}: {m['content']}" for m in messages])
        response = self.llm.invoke(prompt)
        return {"content": response.content}

groq_llm = GroqLLMWrapper(llm)

def get_agents():
    generator = AssistantAgent(
        name="CodeGenerator",
        llm_config={"config_list": [{"model": groq_llm}]},
        system_message="Generate working Python code for the LeetCode problem.",
    )
    reviewer = AssistantAgent(
        name="CodeReviewer",
        llm_config={"config_list": [{"model": groq_llm}]},
        system_message="Review the code and point out flaws or improvements.",
    )
    optimizer = AssistantAgent(
        name="CodeOptimizer",
        llm_config={"config_list": [{"model": groq_llm}]},
        system_message="Optimize the code for time and space efficiency.",
    )
    return generator, reviewer, optimizer
