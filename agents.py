from autogen import Assistant<PERSON><PERSON>, UserProxyAgent, GroupChat, GroupChatManager
from config import Config

def groq_llm_config():
    """Configuration for Groq LLM to work with Autogen"""
    # Validate configuration first
    Config.validate_config()

    return {
        "config_list": [
            {
                "model": Config.GROQ_MODEL,
                "api_key": Config.GROQ_API_KEY,
                "base_url": Config.GROQ_BASE_URL,
                "api_type": "openai"
            }
        ],
        "temperature": Config.AGENT_TEMPERATURE,
        "timeout": Config.AGENT_TIMEOUT,
    }

def get_agents():
    """Create and return the three specialized agents"""

    # Code Generator Agent
    code_generator = AssistantAgent(
        name="CodeGenerator",
        llm_config=groq_llm_config(),
        system_message=Config.CODE_GENERATOR_PROMPT,
        max_consecutive_auto_reply=Config.MAX_CONSECUTIVE_AUTO_REPLY,
    )

    # Code Reviewer Agent
    code_reviewer = AssistantAgent(
        name="CodeReviewer",
        llm_config=groq_llm_config(),
        system_message=Config.CODE_REVIEWER_PROMPT,
        max_consecutive_auto_reply=Config.MAX_CONSECUTIVE_AUTO_REPLY,
    )

    # Code Optimizer Agent
    code_optimizer = AssistantAgent(
        name="CodeOptimizer",
        llm_config=groq_llm_config(),
        system_message=Config.CODE_OPTIMIZER_PROMPT,
        max_consecutive_auto_reply=Config.MAX_CONSECUTIVE_AUTO_REPLY,
    )

    return code_generator, code_reviewer, code_optimizer

def create_group_chat(agents):
    """Create a group chat with the agents"""
    code_generator, code_reviewer, code_optimizer = agents

    # Create a user proxy to manage the conversation
    user_proxy = UserProxyAgent(
        name="UserProxy",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=0,
        code_execution_config=False,
    )

    # Create group chat
    groupchat = GroupChat(
        agents=[user_proxy, code_generator, code_reviewer, code_optimizer],
        messages=[],
        max_round=Config.MAX_GROUP_CHAT_ROUNDS,
        speaker_selection_method="round_robin",
    )

    # Create group chat manager
    manager = GroupChatManager(
        groupchat=groupchat,
        llm_config=groq_llm_config(),
    )

    return user_proxy, manager

def run_leetcode_optimizer(problem_statement):
    """Run the complete LeetCode optimization pipeline"""

    # Get agents
    agents = get_agents()
    user_proxy, manager = create_group_chat(agents)

    # Enhanced problem statement with clear instructions
    enhanced_prompt = f"""
LeetCode Problem to Solve:
{problem_statement}

Please follow this workflow:
1. CodeGenerator: Generate a working solution
2. CodeReviewer: Review the code and provide feedback
3. CodeOptimizer: Optimize the code based on the review

Let's start with the CodeGenerator.
"""

    # Start the conversation
    chat_result = user_proxy.initiate_chat(
        manager,
        message=enhanced_prompt,
        clear_history=True,
    )

    return chat_result
