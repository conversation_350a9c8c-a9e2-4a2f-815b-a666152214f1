from autogen import Assistant<PERSON><PERSON>, UserProxyAgent, GroupChat, GroupChatManager
from config import Config

def groq_llm_config():
    """Configuration for Groq LLM to work with Autogen"""
    # Validate configuration first
    Config.validate_config()

    return {
        "config_list": [
            {
                "model": Config.GROQ_MODEL,
                "api_key": Config.GROQ_API_KEY,
                "base_url": Config.GROQ_BASE_URL,
                "api_type": "openai"
            }
        ],
        "temperature": Config.AGENT_TEMPERATURE,
        "timeout": Config.AGENT_TIMEOUT,
    }

def get_agents():
    """Create and return the three specialized agents"""

    # Code Generator Agent
    code_generator = AssistantAgent(
        name="CodeGenerator",
        llm_config=groq_llm_config(),
        system_message="""You are a LeetCode Code Generator. Your role is to:
1. Analyze the given LeetCode problem carefully
2. Generate clean, working Python code that solves the problem
3. Include proper function signature and return statement
4. Add comments explaining the approach
5. Provide time and space complexity analysis
6. Make sure the code is syntactically correct and follows Python best practices

Format your response as:
```python
# Your solution code here
```

Time Complexity: O(...)
Space Complexity: O(...)
Approach: Brief explanation of your approach
""",
        max_consecutive_auto_reply=1,
    )

    # Code Reviewer Agent
    code_reviewer = AssistantAgent(
        name="CodeReviewer",
        llm_config=groq_llm_config(),
        system_message="""You are a LeetCode Code Reviewer. Your role is to:
1. Review the generated code for correctness
2. Check for edge cases and potential bugs
3. Verify the logic and algorithm implementation
4. Suggest improvements for readability and maintainability
5. Validate the time and space complexity analysis
6. Point out any issues with the code structure

Provide your review in this format:
**Code Review:**
- ✅ Correctness: [Your assessment]
- ✅ Edge Cases: [Your assessment]
- ✅ Logic: [Your assessment]
- 🔧 Suggestions: [Your suggestions]
- ⚠️ Issues Found: [Any issues or "None"]
""",
        max_consecutive_auto_reply=1,
    )

    # Code Optimizer Agent
    code_optimizer = AssistantAgent(
        name="CodeOptimizer",
        llm_config=groq_llm_config(),
        system_message="""You are a LeetCode Code Optimizer. Your role is to:
1. Take the reviewed code and optimize it for better performance
2. Reduce time complexity if possible
3. Optimize space usage
4. Improve code efficiency and readability
5. Provide multiple optimization approaches if available
6. Explain the trade-offs of each optimization

Format your response as:
**Optimized Solution:**
```python
# Your optimized code here
```

**Optimizations Made:**
- [List of optimizations]

**Performance Improvement:**
- Time Complexity: Before O(...) → After O(...)
- Space Complexity: Before O(...) → After O(...)

**Alternative Approaches:** (if any)
[Brief description of other possible optimizations]
""",
        max_consecutive_auto_reply=1,
    )

    return code_generator, code_reviewer, code_optimizer

def create_group_chat(agents):
    """Create a group chat with the agents"""
    code_generator, code_reviewer, code_optimizer = agents

    # Create a user proxy to manage the conversation
    user_proxy = UserProxyAgent(
        name="UserProxy",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=0,
        code_execution_config=False,
    )

    # Create group chat
    groupchat = GroupChat(
        agents=[user_proxy, code_generator, code_reviewer, code_optimizer],
        messages=[],
        max_round=6,  # Generator -> Reviewer -> Optimizer -> potential back and forth
        speaker_selection_method="round_robin",
    )

    # Create group chat manager
    manager = GroupChatManager(
        groupchat=groupchat,
        llm_config=groq_llm_config(),
    )

    return user_proxy, manager

def run_leetcode_optimizer(problem_statement):
    """Run the complete LeetCode optimization pipeline"""

    # Get agents
    agents = get_agents()
    user_proxy, manager = create_group_chat(agents)

    # Enhanced problem statement with clear instructions
    enhanced_prompt = f"""
LeetCode Problem to Solve:
{problem_statement}

Please follow this workflow:
1. CodeGenerator: Generate a working solution
2. CodeReviewer: Review the code and provide feedback
3. CodeOptimizer: Optimize the code based on the review

Let's start with the CodeGenerator.
"""

    # Start the conversation
    chat_result = user_proxy.initiate_chat(
        manager,
        message=enhanced_prompt,
        clear_history=True,
    )

    return chat_result
