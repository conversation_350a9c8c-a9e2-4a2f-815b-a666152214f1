from autogen import <PERSON><PERSON><PERSON>, UserProxyAgent, GroupChat, GroupChatManager
from config import Config

def groq_llm_config():
    """Configuration for Groq LLM to work with Autogen"""
    # Validate configuration first
    Config.validate_config()

    return {
        "config_list": [
            {
                "model": Config.GROQ_MODEL,
                "api_key": Config.GROQ_API_KEY,
                "base_url": Config.GROQ_BASE_URL,
                "api_type": "openai"
            }
        ],
        "temperature": Config.AGENT_TEMPERATURE,
        "timeout": Config.AGENT_TIMEOUT,
    }

def get_agents():
    """Create and return the five specialized agents with feedback loop support"""

    # Code Generator Agent (Enhanced for feedback)
    code_generator = AssistantAgent(
        name="CodeGenerator",
        llm_config=groq_llm_config(),
        system_message=Config.CODE_GENERATOR_PROMPT + """

**FEEDBACK LOOP INSTRUCTIONS:**
When you receive feedback about failed test cases:
1. Carefully analyze the specific failures mentioned
2. Identify the root cause of each failure
3. Generate an improved solution that addresses ALL the issues
4. Pay special attention to edge cases that previously failed
5. Ensure your new solution handles the specific scenarios mentioned in the feedback
""",
        max_consecutive_auto_reply=Config.MAX_CONSECUTIVE_AUTO_REPLY,
    )

    # Code Reviewer Agent
    code_reviewer = AssistantAgent(
        name="CodeReviewer",
        llm_config=groq_llm_config(),
        system_message=Config.CODE_REVIEWER_PROMPT,
        max_consecutive_auto_reply=Config.MAX_CONSECUTIVE_AUTO_REPLY,
    )

    # Test Validator Agent
    test_validator = AssistantAgent(
        name="TestValidator",
        llm_config=groq_llm_config(),
        system_message=Config.TEST_VALIDATOR_PROMPT,
        max_consecutive_auto_reply=Config.MAX_CONSECUTIVE_AUTO_REPLY,
    )

    # Feedback Coordinator Agent
    feedback_coordinator = AssistantAgent(
        name="FeedbackCoordinator",
        llm_config=groq_llm_config(),
        system_message=Config.FEEDBACK_COORDINATOR_PROMPT,
        max_consecutive_auto_reply=Config.MAX_CONSECUTIVE_AUTO_REPLY,
    )

    # Code Optimizer Agent
    code_optimizer = AssistantAgent(
        name="CodeOptimizer",
        llm_config=groq_llm_config(),
        system_message=Config.CODE_OPTIMIZER_PROMPT,
        max_consecutive_auto_reply=Config.MAX_CONSECUTIVE_AUTO_REPLY,
    )

    return code_generator, code_reviewer, test_validator, feedback_coordinator, code_optimizer

def create_group_chat(agents):
    """Create a group chat with the agents including feedback loop support"""
    code_generator, code_reviewer, test_validator, feedback_coordinator, code_optimizer = agents

    # Create a user proxy to manage the conversation
    user_proxy = UserProxyAgent(
        name="UserProxy",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=0,
        code_execution_config=False,
    )

    # Create group chat with enhanced workflow and feedback loop
    groupchat = GroupChat(
        agents=[user_proxy, code_generator, code_reviewer, test_validator, feedback_coordinator, code_optimizer],
        messages=[],
        max_round=Config.MAX_GROUP_CHAT_ROUNDS,
        speaker_selection_method="auto",  # Changed to auto for better feedback loop control
    )

    # Create group chat manager
    manager = GroupChatManager(
        groupchat=groupchat,
        llm_config=groq_llm_config(),
    )

    return user_proxy, manager

def run_leetcode_optimizer(problem_statement):
    """Run the complete LeetCode optimization pipeline"""

    # Get agents
    agents = get_agents()
    user_proxy, manager = create_group_chat(agents)

    # Enhanced problem statement with clear instructions
    enhanced_prompt = f"""
LeetCode Problem to Solve:
{problem_statement}

Please follow this enhanced workflow with FEEDBACK LOOP for LeetCode acceptance:

**INITIAL WORKFLOW:**
1. CodeGenerator: Generate a working solution with proper edge case handling
2. CodeReviewer: Review the code for correctness and improvements
3. TestValidator: Create comprehensive test cases and validate the solution

**FEEDBACK LOOP (if tests fail):**
4. FeedbackCoordinator: Analyze failed tests and provide specific feedback
5. CodeGenerator: Regenerate solution based on feedback
6. Repeat steps 2-5 until all tests pass (max 3 iterations)

**FINAL STEP:**
7. CodeOptimizer: Optimize the validated solution

The goal is to ensure the solution will be ACCEPTED by LeetCode through iterative improvement.

Let's start with the CodeGenerator.
"""

    # Start the conversation
    chat_result = user_proxy.initiate_chat(
        manager,
        message=enhanced_prompt,
        clear_history=True,
    )

    return chat_result
