import streamlit as st
import time
from agents import run_leetcode_optimizer
from config import Config
from utils import (
    parse_chat_history,
    validate_problem_input,
    create_agent_summary,
    export_results_to_markdown,
    display_code_with_syntax_highlighting,
    extract_code_blocks
)
from test_executor import TestExecutor, format_test_results

# Page configuration
st.set_page_config(
    page_title=Config.PAGE_TITLE,
    layout=Config.PAGE_LAYOUT,
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1f77b4;
        margin-bottom: 2rem;
    }
    .agent-response {
        border-left: 4px solid #1f77b4;
        padding-left: 1rem;
        margin: 1rem 0;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
    .code-block {
        background-color: #2d3748;
        color: #e2e8f0;
        padding: 1rem;
        border-radius: 5px;
        font-family: 'Courier New', monospace;
    }
    .success-message {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Main title and description
st.markdown('<h1 class="main-header">🧠 LeetCode Multi-Agent Optimizer</h1>', unsafe_allow_html=True)

# Sidebar with information
with st.sidebar:
    st.markdown("## 🤖 Enhanced Agent Workflow")
    st.markdown("""
    **1. Code Generator** 🔧
    - Analyzes the problem
    - Generates initial solution
    - Handles edge cases
    - Provides complexity analysis

    **2. Code Reviewer** 🔍
    - Reviews code correctness
    - Checks edge cases
    - Suggests improvements
    - Validates logic

    **3. Test Validator** 🧪
    - Creates comprehensive test cases
    - Validates against examples
    - Tests edge cases
    - Ensures LeetCode readiness

    **4. Code Optimizer** ⚡
    - Optimizes performance
    - Reduces complexity
    - Provides alternatives
    - Final validation
    """)

    st.markdown("## ⚙️ Configuration")
    st.info("Using Groq AI with Llama3-70B model")

    st.markdown("## 📝 Tips")
    st.markdown("""
    - Paste the complete problem statement
    - Include examples and constraints
    - Be specific about requirements
    """)

# Main content area
col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("### 🧩 Enter your LeetCode problem statement:")

    # Example problems for quick testing
    example_problems = Config.EXAMPLE_PROBLEMS

    selected_example = st.selectbox("Choose an example problem (optional):", ["Custom Problem"] + list(example_problems.keys()))

    if selected_example != "Custom Problem":
        problem_input = st.text_area(
            "LeetCode Problem",
            value=example_problems[selected_example],
            height=300,
            placeholder="Paste the full problem description here..."
        )
    else:
        problem_input = st.text_area(
            "LeetCode Problem",
            height=300,
            placeholder="Paste the full problem description here..."
        )

with col2:
    st.markdown("### 🚀 Actions")
    run_button = st.button("🚀 Run Multi-Agent Optimizer", type="primary", use_container_width=True)

    if st.button("🗑️ Clear", use_container_width=True):
        st.rerun()

# Main processing logic
if run_button and problem_input.strip():
    with st.spinner("🤖 Multi-agents are working on your problem..."):
        try:
            # Progress tracking
            progress_bar = st.progress(0)
            status_text = st.empty()

            status_text.text("🔧 Code Generator is analyzing the problem...")
            progress_bar.progress(15)
            time.sleep(1)

            # Run the multi-agent system
            chat_result = run_leetcode_optimizer(problem_input)

            status_text.text("🔍 Code Reviewer is examining the solution...")
            progress_bar.progress(40)
            time.sleep(1)

            status_text.text("🧪 Test Validator is creating comprehensive test cases...")
            progress_bar.progress(65)
            time.sleep(1)

            status_text.text("⚡ Code Optimizer is improving the solution...")
            progress_bar.progress(85)
            time.sleep(1)

            progress_bar.progress(100)
            status_text.text("✅ All agents have completed their work!")

            # Display results
            st.markdown("---")
            st.markdown("## 💬 Agent Conversations")

            # Parse and display the conversation
            if hasattr(chat_result, 'chat_history') and chat_result.chat_history:
                for i, message in enumerate(chat_result.chat_history):
                    if isinstance(message, dict):
                        name = message.get('name', 'Unknown')
                        content = message.get('content', '')

                        # Skip empty messages
                        if not content.strip():
                            continue

                        # Style different agents differently
                        if name == "CodeGenerator":
                            st.markdown(f"### 🔧 {name}")
                            with st.container():
                                st.markdown(f'<div class="agent-response">{content}</div>', unsafe_allow_html=True)
                        elif name == "CodeReviewer":
                            st.markdown(f"### 🔍 {name}")
                            with st.container():
                                st.markdown(f'<div class="agent-response">{content}</div>', unsafe_allow_html=True)
                        elif name == "TestValidator":
                            st.markdown(f"### 🧪 {name}")
                            with st.container():
                                st.markdown(f'<div class="agent-response">{content}</div>', unsafe_allow_html=True)

                                # Execute actual tests if code is present
                                code_blocks = extract_code_blocks(content)
                                if code_blocks:
                                    with st.expander("🔬 Live Test Execution"):
                                        executor = TestExecutor()
                                        test_results = executor.run_all_tests(code_blocks[0], problem_input)
                                        formatted_results = format_test_results(test_results)
                                        st.markdown(formatted_results)

                        elif name == "CodeOptimizer":
                            st.markdown(f"### ⚡ {name}")
                            with st.container():
                                st.markdown(f'<div class="agent-response">{content}</div>', unsafe_allow_html=True)
                        else:
                            st.markdown(f"### 👤 {name}")
                            st.markdown(content)

                        st.markdown("---")

            # Success message
            st.markdown('<div class="success-message">🎉 <strong>Success!</strong> Your LeetCode problem has been solved, reviewed, tested, and optimized by our enhanced multi-agent system!</div>', unsafe_allow_html=True)

            # Add final validation summary
            st.markdown("### 📋 Final Validation Summary")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Agents Involved", "4", "Code Gen → Review → Test → Optimize")
            with col2:
                st.metric("Workflow Steps", "Complete", "✅ All phases executed")
            with col3:
                st.metric("LeetCode Ready", "Validated", "🧪 Comprehensive testing")

        except Exception as e:
            st.error(f"❌ An error occurred: {str(e)}")
            st.markdown("**Troubleshooting tips:**")
            st.markdown("- Make sure your GROQ_API_KEY is set in the .env file")
            st.markdown("- Check your internet connection")
            st.markdown("- Verify the problem statement is complete")

elif run_button and not problem_input.strip():
    st.warning("⚠️ Please enter a LeetCode problem statement before running the optimizer.")

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; margin-top: 2rem;">
    <p>🤖 Powered by Enhanced Autogen Multi-Agent System | 🚀 Groq AI | 💻 Streamlit</p>
    <p><em>Generate • Review • Test • Optimize • Validate</em></p>
    <p>🎯 <strong>LeetCode Acceptance Ready</strong></p>
</div>
""", unsafe_allow_html=True)
