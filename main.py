import streamlit as st
from autogen import Assistant<PERSON>gent
from agents import get_agents

st.set_page_config(page_title="LeetCode Optimizer 💻⚙️", layout="centered")

st.title("🧠 LeetCode Optimizer")
st.markdown("### 🧩 Enter your LeetCode problem statement below:")

problem_input = st.text_area("LeetCode Problem", height=250, placeholder="Paste the full problem description here...")

if st.button("🚀 Run Optimizer") and problem_input.strip() != "":
    with st.spinner("Agents are cooking... 🔥"):
    
        generator, reviewer, optimizer = get_agents()
        user = AssistantAgent(name="User")

        
        messages = []

        
        result = user.initiate_chat(
            recipient=generator,
            message=problem_input,
            recipient_list=[reviewer, optimizer],
        )

        
        st.markdown("---")
        st.markdown("### 💬 Agent Conversations")

        for m in result.chat_history:
            role = m.get("role", "assistant").capitalize()
            content = m.get("content", "")
            st.markdown(f"**{role}:**\n```\n{content.strip()}\n```", unsafe_allow_html=True)

        st.success("Done! Code has been generated, reviewed, and optimized.")
