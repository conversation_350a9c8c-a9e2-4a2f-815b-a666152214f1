# 🧠 LeetCode Multi-Agent Optimizer

A powerful multi-agent system that uses **Autogen**, **Groq AI**, and **Streamlit** to solve, review, and optimize LeetCode problems automatically.

## 🤖 Multi-Agent System

This application uses three specialized AI agents:

1. **🔧 Code Generator**: Analyzes problems and generates initial solutions
2. **🔍 Code Reviewer**: Reviews code for correctness and suggests improvements  
3. **⚡ Code Optimizer**: Optimizes code for better performance and efficiency

## 🚀 Features

- **Multi-Agent Collaboration**: Three specialized agents work together
- **Real-time Processing**: Watch agents collaborate in real-time
- **Code Analysis**: Complete time/space complexity analysis
- **Interactive UI**: Beautiful Streamlit interface
- **Example Problems**: Pre-loaded example problems for quick testing
- **Groq AI Integration**: Fast inference using Groq's infrastructure

## 📋 Prerequisites

- Python 3.8 or higher
- Groq API key (free at [console.groq.com](https://console.groq.com/keys))

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <your-repo-url>
   cd Leetcode-Opti
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your Groq API key:
   ```
   GROQ_API_KEY=your_actual_groq_api_key_here
   ```

## 🎯 Usage

1. **Start the application**:
   ```bash
   streamlit run main.py
   ```

2. **Open your browser** and go to `http://localhost:8501`

3. **Enter a LeetCode problem** or select from example problems

4. **Click "Run Multi-Agent Optimizer"** and watch the agents work!

## 📝 Example Problems

The app includes pre-loaded examples:
- Two Sum
- Valid Parentheses
- (Add your own custom problems)

## 🔧 How It Works

1. **Problem Input**: User provides a LeetCode problem statement
2. **Code Generation**: First agent analyzes and generates a solution
3. **Code Review**: Second agent reviews for correctness and improvements
4. **Code Optimization**: Third agent optimizes for performance
5. **Results Display**: All conversations and final optimized code shown

## 🏗️ Project Structure

```
Leetcode-Opti/
├── main.py              # Streamlit UI and main application
├── agents.py            # Multi-agent system configuration
├── requirements.txt     # Python dependencies
├── .env.example        # Environment variables template
└── README.md           # This file
```

## ⚙️ Configuration

### Groq AI Settings
- Model: `llama3-70b-8192`
- Temperature: `0.7`
- Timeout: `120 seconds`

### Agent Settings
- Max consecutive replies: `1` per agent
- Group chat rounds: `6` maximum
- Speaker selection: `round_robin`

## 🐛 Troubleshooting

**Common Issues:**

1. **"GROQ_API_KEY not found"**
   - Make sure you created `.env` file with your API key

2. **Import errors**
   - Run `pip install -r requirements.txt`

3. **Slow responses**
   - Groq AI is usually fast, check your internet connection

4. **Agent not responding**
   - Try refreshing the page and running again

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **Autogen**: Microsoft's multi-agent framework
- **Groq**: Fast AI inference platform
- **Streamlit**: Beautiful web app framework
- **LeetCode**: Problem inspiration

---

**Happy Coding! 🚀**
